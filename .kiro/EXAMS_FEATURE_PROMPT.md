I want to create an Exam Dashboard as an extension to my existing productivity platform IsotopeAI for students. The goal is to allow students to choose the exam(s) they are preparing for — whether it’s Engineering (like JEE, BITSAT, VITEEE, etc.), Medical (like NEET, AIIMS), school-level boards (CBSE, ICSE, State Boards), high school (SAT, ACT, A-levels), college entrance tests, competitive exams (UPSC, SSC, CAT, GRE, GMAT), or custom course-based exams.

When the student selects an exam, the dashboard should:

1. Exam Setup & Selection
Let users search or browse a list of exams by category (Engineering, Medical, School, Competitive, International, Custom).
Support multiple exam tracking (e.g., JEE + Boards together).
Store exam metadata: exam type, pattern, duration, marking scheme, subjects tested.
Option to create a custom exam by entering syllabus & pattern manually.
2. Course Structure & Syllabus Loading
Auto-load official syllabus for the chosen exam from a structured database.
Structure the syllabus into:
Grade level (if applicable)
Subject-wise (Physics, Chemistry, Biology, Math, etc.)
Unit-wise / Chapter-wise
Topic-wise
Sub-topic / Concept level
Include exam-specific weightage per subject/chapter/topic (pulled from past year papers).
Allow students to hide irrelevant chapters (for droppers or cross-exam preppers).
Attach recommended resources (books, notes, online videos, question banks) per topic.
3. Syllabus Progress Tracking (Integrated with Existing Tasks Feature)
Ability to mark topics as Not Started / In Progress / Completed / Revised.
Show percentage completion at all levels (subject, chapter, topic).
Link syllabus items directly to tasks in the IsotopeAI Tasks feature.
Add revision tracking (multiple completion cycles with date logs).
Allow custom notes per topic.
4. Exam Strategy & Timeline
AI-generated study plan based on:
Time left until exam
Syllabus completion status
Student’s available daily hours (from productivity tracker)
Adaptive scheduling: if a student falls behind, reschedule automatically.
Exam countdown with milestone reminders.
Mock test planner integrated into the timeline.
5. Performance Analytics
Visual graphs for syllabus completion over time.
Subject-wise strength/weakness analytics.
Time spent per subject/topic (from productivity tracker data).
Comparison with past weeks/months progress.
Exam readiness meter (AI-based confidence score).
6. Additional Features
Multiple syllabus views: Tree view, List view, Calendar view.
Search & Filter: Quickly find chapters/topics.
Downloadable & Printable progress reports.
Offline mode for syllabus tracking (sync when online).
Gamification: XP points for completing topics, badges for milestones.
Integration with group study rooms (already in IsotopeAI) for collaborative tracking.
7. Admin & Data Management
Central syllabus database for major exams with regular updates.
API support to fetch syllabi from official sources where possible.
Version control for syllabus (in case official changes happen mid-year).
Role-based access (e.g., teachers can assign syllabus portions to students).

Key Output of the Prompt:
This Exam Dashboard should be an intelligent, dynamic, and deeply integrated syllabus & exam preparation tracker that blends seamlessly into IsotopeAI’s productivity ecosystem — giving students a single, central hub to manage all aspects of their exam journey.

also in start, when user opens the page for first time, add user journey/walkthrough. also add that the student should be prompted to select exam while profile-setup only(for new users). add that the UI should be heavily inspired only by the tasks page - as its the best UI.

Of course. Here is a comprehensive Product Requirements Document (PRD) for the IsotopeAI Exam Dashboard, based on your detailed request.
Product Requirements Document: IsotopeAI Exam Dashboard
Document Version: 1.0
Author: Perplexity AI (for IsotopeAI Product Team)
Date: August 10, 2025
Status: Proposed
1. Overview & Vision
This document outlines the requirements for the Exam Dashboard, a new core feature for the IsotopeAI productivity platform. The vision is to create an intelligent, dynamic, and deeply integrated syllabus and exam preparation tracker. This will provide students with a single, central hub to manage all aspects of their exam journey, from initial planning to final revision, seamlessly blending with IsotopeAI’s existing productivity ecosystem.
2. Problem Statement
Students preparing for high-stakes exams face several challenges:
Information Overload: Syllabi, exam patterns, and recommended resources are scattered across multiple websites, books, and coaching materials.
Lack of Structure: Students struggle to break down vast syllabi into a manageable, trackable study plan.
Inefficient Tracking: Using spreadsheets or notebooks to track syllabus completion is manual, static, and disconnected from their daily task management.
Static Planning: Study plans often become obsolete when a student falls behind or their priorities change, leading to stress and demotivation.
No Centralized View: Preparing for multiple exams simultaneously (e.g., JEE + BITSAT + Boards) requires juggling different syllabi and priorities, with no unified dashboard to monitor progress.
The Exam Dashboard will solve these problems by providing a structured, automated, and adaptive preparation environment within IsotopeAI.
3. Target Audience & Personas
The primary audience is students preparing for a wide range of academic and competitive examinations.
Persona 1: Aryan (The Multi-Exam Aspirant)
Bio: A 12th-grade student from Ghaziabad, preparing for Engineering entrance exams (JEE Main & Advanced) alongside his CBSE Board exams.
Needs: A way to track overlapping and distinct topics for both JEE and Boards, a clear timeline to balance both, and analytics to identify weak areas in Physics.
Persona 2: Priya (The Focused Aspirant)
Bio: A dropper preparing exclusively for the NEET medical entrance exam.
Needs: A detailed, topic-wise syllabus tracker with revision logs, an adaptive study plan that adjusts to her pace, and integrated mock test scheduling.
Persona 3: Sameer (The Professional Aspirant)
Bio: A working professional studying for the CAT exam.
Needs: A flexible planner that works around his job schedule, performance analytics to optimize his limited study time, and the ability to add custom resources to his syllabus.
4. Goals & Success Metrics
GoalSuccess Metric
Increase User Engagement
- 70% of active student users adopt the Exam Dashboard within 3 months. 
- Increase in average session duration by 15%.
Enhance User Retention
- Decrease in 60-day user churn by 20% for the student segment.
Drive New User Acquisition
- The Exam Dashboard becomes a key feature highlighted in marketing campaigns, leading to a 10% increase in new student sign-ups.
Establish Feature Value
- At least 50% of dashboard users utilize the adaptive study plan and progress tracking features daily.
5. Feature Requirements
5.1. Onboarding & Exam Selection (The First-Time Experience)
New User Onboarding:
During the initial profile setup, after basic details, new users will be prompted with a mandatory step: "Which exam(s) are you preparing for?"
This will launch a simplified version of the exam selection UI (see 5.2).
Selecting an exam will automatically pre-configure their IsotopeAI workspace with the Exam Dashboard and its corresponding syllabus.
Existing User Onboarding (Walkthrough):
The first time an existing user visits the new "Exams" tab, a guided walkthrough will be initiated.
Step 1 (Welcome): A modal introducing the Exam Dashboard. "Manage your entire exam preparation in one place."
Step 2 (Select Exam): The user is prompted to search for and select their primary exam.
Step 3 (Syllabus View): A tooltip highlights the auto-loaded syllabus structure. "Your entire syllabus, broken down by subject, chapter, and topic."
Step 4 (Progress Tracking): Another tooltip points to the status markers. "Track your progress from 'Not Started' to 'Revised'."
Step 5 (AI Study Plan): A final tooltip points to the timeline/calendar view. "Get a personalized, adaptive study plan based on your schedule."
5.2. Exam Setup & Management
Exam Database: A comprehensive, searchable list of exams categorized into: Engineering, Medical, School Boards (CBSE, ICSE, UP Board, etc.), Competitive (UPSC, SSC, CAT), International (SAT, ACT), and more.
Multi-Exam Tracking: Users can select and track multiple exams simultaneously. The UI will intelligently highlight common and unique chapters across the selected syllabi.
Exam Metadata: The system will store and display key information for each exam: pattern, duration, marking scheme, subjects, and official website links.
Custom Exam Creation: A feature to allow users to create a "Custom Exam" where they can manually input:
Exam Name
Subjects
Syllabus structure (by adding Chapters and Topics)
5.3. Course Structure & Syllabus Management
Auto-Loading: Upon exam selection, the official syllabus is loaded from a central database.
Hierarchical Structure: The syllabus must be displayed in a collapsible tree structure:
Exam (e.g., JEE Main)
Subject (e.g., Physics)
Unit/Chapter (e.g., Electrostatics)
Topic (e.g., Coulomb's Law)
Sub-topic/Concept (e.g., Principle of Superposition)
Topic Weightage: Display an indicator (e.g., a tag like High Priority) next to topics/chapters based on their historical importance, derived from past paper analysis.
Syllabus Customization: Allow users to hide or disable specific chapters/topics that are not relevant to them (e.g., for droppers who have already completed certain portions).
Resource Attachment: Each topic/sub-topic level will have a section to attach resources:
Default: Pre-linked recommended books, official notes.
User-Added: Links to online videos, personal notes (linking to IsotopeAI's Notes feature), and custom files.
5.4. Syllabus Progress Tracking
Status Markers: Each topic/sub-topic will have a status that the user can set: Not Started, In Progress, Completed, Revised.
Percentage Completion: Progress bars will dynamically show percentage completion at the Subject, Chapter, and Overall Exam levels.
Task Integration: A core requirement.
Each syllabus item (e.g., "Coulomb's Law") can be directly converted into a task or linked to an existing task in the IsotopeAI Tasks feature.
Completing the linked task can automatically update the syllabus status to Completed.
Revision Tracking: When a user marks a Completed topic as Revised, the system will log the revision date. It will support multiple revision cycles (e.g., Revision 1: Aug 15, 2025; Revision 2: Sep 20, 2025).
In-line Notes: A small notes field for each topic for quick summaries or formulas.
5.5. AI-Powered Strategy & Timeline
AI Study Plan Generation: An AI engine will generate a daily/weekly study schedule based on:
Inputs: Exam date(s), current syllabus completion status, user-defined available study hours per day (from IsotopeAI's productivity tracker), and topic weightage.
Output: A personalized timeline suggesting which topics to study each day to complete the syllabus on time.
Adaptive Rescheduling: If a user misses a scheduled task or marks a topic as taking longer than expected, the AI will automatically re-balance the upcoming schedule.
Exam Countdown & Milestones: A prominent countdown timer to the exam date. The AI plan will set key milestones (e.g., "Complete 50% of Physics by Sep 30").
Mock Test Planner: Users can schedule mock tests within the timeline. The system will recommend dates for full-length and topic-wise tests based on syllabus completion.
5.6. Performance Analytics Dashboard
Syllabus Completion Graph: A line chart showing overall syllabus completion (%) vs. time.
Subject-wise Analytics: A radar or bar chart showing completion levels and strengths/weaknesses across different subjects.
Time Allocation Analysis: (Requires integration with the productivity tracker) A pie chart showing time spent per subject, allowing users to see if their effort aligns with their plan.
Readiness Meter: An AI-generated confidence score (e.g., 78% Ready) based on syllabus completion, revision cycles, and mock test performance (future integration).
5.7. UI/UX & Additional Features
UI Inspiration: The UI/UX for the Exam Dashboard must be heavily inspired by the existing IsotopeAI Tasks page. This includes the layout, typography, color scheme, use of modals, and interaction patterns to ensure a cohesive user experience.
Multiple Views:
Tree View: Default hierarchical view of the syllabus.
List View: A flattened, sortable list of all topics.
Calendar View: Displays the AI-generated study plan and scheduled mock tests.
Search & Filter: A powerful search bar to quickly find any chapter or topic. Filters for status, subject, and priority.
Reporting: A feature to generate and download/print a PDF progress report.
Offline Mode: Users should be able to view the syllabus and update their progress offline. Data will sync automatically when they reconnect.
Gamification: Award XP for completing topics/chapters and badges for achieving milestones (e.g., "Physics Pro," "50% Syllabus Complete").
Group Study Integration: Link syllabus progress to IsotopeAI's Group Study Rooms, allowing study partners to view and discuss each other's progress.
5.8. Admin & Technical Requirements
Central Syllabus Database: A scalable, version-controlled database to house all exam syllabi and metadata. It must be easy for the admin team to update.
Syllabus Update Workflow: A system for updating syllabi when official changes are announced. This should include versioning, so students can choose to stick with the old syllabus or migrate to the new one.
API Support: Where feasible, use APIs to fetch syllabus information directly from official sources to ensure accuracy.
Role-Based Access: An admin panel with role-based access for content managers to update syllabi and for future features like teachers assigning syllabus portions.
Dependencies: This feature is critically dependent on:
IsotopeAI User Authentication
IsotopeAI Tasks Feature (for deep integration)
IsotopeAI Productivity Tracker (for time analytics and AI planning)
IsotopeAI Group Study Rooms


Alright Arnav,
I’ve converted your feature list into a full PRD (Product Requirements Document) for the IsotopeAI Exam Dashboard so that it’s detailed enough for dev handover while keeping your vision intact.
Main structure: Overview → Objectives → User Personas → User Flow → Feature Requirements → UI/UX → Technical Requirements → Analytics → Risks → Future Scope.
PRD: IsotopeAI Exam Dashboard Extension
1. Overview
The IsotopeAI Exam Dashboard will be a fully integrated extension of the existing IsotopeAI productivity platform, designed for students preparing for competitive, board-level, or custom exams.
It will serve as a centralized hub for syllabus management, progress tracking, AI-powered scheduling, analytics, and collaborative preparation — directly tied to IsotopeAI’s task management, productivity tracking, and group study features.
2. Objectives
Centralize exam preparation tracking for any type of exam.
Provide official syllabus auto-loading with structured breakdown.
Deliver real-time analytics on progress, strengths, and weaknesses.
Integrate with IsotopeAI’s existing productivity tools for seamless planning.
Enhance motivation and retention with gamification and collaborative tools.
3. Target User Personas
Engineering Aspirants (JEE, BITSAT, VITEEE)
Medical Aspirants (NEET, AIIMS)
School Students (CBSE, ICSE, State Boards)
International Students (SAT, ACT, A-levels, IB)
Competitive Exam Candidates (UPSC, SSC, CAT, GRE, GMAT)
Custom Exam Users (university entrance, private course assessments)
4. User Journey
For New Users (Profile Setup)
User signs up → Profile Setup prompts “Select your exam(s)”.
User browses or searches exams by category.
For custom exams: enters syllabus & pattern manually.
User walkthrough/tutorial starts → explains syllabus tracking, task linking, and analytics.
For Returning Users
User opens Exam Dashboard.
Sees overview card for each selected exam with:
Countdown timer
Progress bar
AI readiness score
Can dive into syllabus, analytics, study plan, and resources.
5. Core Feature Requirements
5.1 Exam Setup & Selection
Browse/Search exams by category.
Multi-exam tracking support.
Exam metadata storage:
Type, pattern, duration, marking scheme, subjects tested.
Custom exam creation with manual syllabus entry.
Version control for official syllabus changes.
5.2 Course Structure & Syllabus Loading
Auto-load syllabus from central database.
Structure levels:
Grade (if applicable)
Subject
Unit/Chapter
Topic
Sub-topic/Concept
Exam-specific weightage data from PYQs.
Hide irrelevant chapters.
Attach recommended resources per topic.
5.3 Syllabus Progress Tracking
Topic statuses: Not Started / In Progress / Completed / Revised.
Multi-level % completion tracking.
Link topics to IsotopeAI Tasks.
Revision logging with multiple cycles.
Custom topic notes.
5.4 Exam Strategy & Timeline
AI-generated study plan:
Based on time left, syllabus status, daily available hours.
Adaptive rescheduling if student falls behind.
Milestone reminders and countdown.
Mock test planner.
5.5 Performance Analytics
Graphs for syllabus completion trends.
Subject-wise strengths & weaknesses.
Time spent per subject/topic.
Past progress comparisons.
AI readiness meter.
5.6 Additional Features
Multiple syllabus views: Tree / List / Calendar.
Search & filter by subject/chapter/topic.
Downloadable/printable reports.
Offline mode with sync.
Gamification: XP points, badges, leaderboards.
Integration with Group Study Rooms for collaborative tracking.
5.7 Admin & Data Management
Central syllabus database with regular updates.
API integration for fetching official syllabi.
Role-based access for teachers.
6. UI/UX Requirements
UI theme consistent with Tasks Page (dark theme, violet/purple/rose/emerald highlights).
First-time walkthrough with interactive guide.
Exam cards layout for multi-exam tracking.
Progress rings for subject completion.
Collapsible syllabus tree view.
Drag-and-drop to reorder tasks/milestones.
Responsive design for mobile/tablet/desktop.
7. Technical Requirements
Backend:
Use existing IsotopeAI infrastructure (Firebase for auth/data).
Add new Firestore collections for exams, syllabus, progress, analytics.
APIs for syllabus fetch, progress update, analytics calculations.
Frontend:
React + Tailwind (matching IsotopeAI’s existing stack).
Offline data caching with IndexedDB.
Integrations:
AI model for readiness scoring & scheduling.
Group study sync API.
8. Analytics & KPIs
Daily Active Users (DAU) for Exam Dashboard.
% syllabus completion rate.
Average time spent on dashboard.
Exam readiness improvement over time.
Mock test participation rate.
9. Risks & Mitigation
Risk: Official syllabus updates mid-prep → Mitigation: version control & update notifications.
Risk: Data overload for students → Mitigation: personalized dashboard views.
Risk: Multi-exam overlap confusion → Mitigation: color-coded exam tracking.
10. Future Scope
AI-based question recommendation engine.
Integration with external mock test providers.
Peer-to-peer study matching for same exam level.
Auto-import school/college timetables into study plan.
If you want, I can now convert this PRD into a clickable wireframe so you can see how the UI will look based on your current Tasks page design style. That would make dev handover super smooth.

